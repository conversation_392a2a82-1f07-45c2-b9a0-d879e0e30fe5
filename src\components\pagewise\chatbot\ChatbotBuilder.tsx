import React, { useState } from "react";
import { Plus, Zap, MessageSquare, Users, Clock } from "lucide-react";
import { But<PERSON> } from "@/components/shared/button";
import { Card } from "@/components/shared/card";
import FlowList from "./FlowList";

const ChatbotBuilder = () => {
  const [flows, setFlows] = useState([
    {
      id: 1,
      name: "Welcome Flow",
      status: "active" as const,
      triggers: 3,
      responses: 12,
    },
    {
      id: 2,
      name: "Support Bot",
      status: "draft" as const,
      triggers: 2,
      responses: 8,
    },
    {
      id: 3,
      name: "Order Tracking",
      status: "active" as const,
      triggers: 1,
      responses: 5,
    },
  ]);

  const handleEditFlow = (flowId: number) => {
    console.log("Edit flow:", flowId);
    // Add your edit flow logic here
  };

  const handleRunFlow = (flowId: number) => {
    console.log("Run flow:", flowId);
    // Add your run flow logic here
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Chatbot Builder</h1>
          <p className="text-gray-600 mt-1">
            Create and manage automated conversation flows
          </p>
        </div>
        <Button className="ayuchat-button">
          <Plus className="w-4 h-4 mr-2" />
          New Flow
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active Flows</p>
              <p className="text-2xl font-bold text-gray-900">6</p>
            </div>
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <Zap className="w-5 h-5 text-green-600" />
            </div>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Triggers</p>
              <p className="text-2xl font-bold text-gray-900">24</p>
            </div>
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-5 h-5 text-blue-600" />
            </div>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Users Engaged</p>
              <p className="text-2xl font-bold text-gray-900">1,234</p>
            </div>
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <Users className="w-5 h-5 text-purple-600" />
            </div>
          </div>
        </Card>
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg. Response Time</p>
              <p className="text-2xl font-bold text-gray-900">2.3s</p>
            </div>
            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
              <Clock className="w-5 h-5 text-orange-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Flow Builder */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Flow List Component */}
        <FlowList
          flows={flows}
          onEditFlow={handleEditFlow}
          onRunFlow={handleRunFlow}
        />

        {/* New compontent create in there make it as */}
        {/* Visual Flow Builder Preview */}
        <div className="lg:col-span-1">
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Flow Builder
            </h3>
            <div className="space-y-4">
              <div className="bg-gradient-to-r from-teal-50 to-cyan-50 border border-teal-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-3 h-3 bg-teal-500 rounded-full"></div>
                  <span className="text-sm font-medium text-teal-700">
                    Welcome Trigger
                  </span>
                </div>
                <p className="text-xs text-teal-600">When user says "hello"</p>
              </div>

              <div className="flex justify-center">
                <div className="w-px h-8 bg-gray-300"></div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium text-blue-700">
                    Send Message
                  </span>
                </div>
                <p className="text-xs text-blue-600">
                  "Hi! How can I help you today?"
                </p>
              </div>

              <div className="flex justify-center">
                <div className="w-px h-8 bg-gray-300"></div>
              </div>

              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-sm font-medium text-purple-700">
                    Quick Replies
                  </span>
                </div>
                <p className="text-xs text-purple-600">
                  Support • Sales • Other
                </p>
              </div>
            </div>

            <Button className="w-full mt-4 ayuchat-button">
              <Plus className="w-4 h-4 mr-2" />
              Add Block
            </Button>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ChatbotBuilder;
