import React from 'react'

const Message = () => {
  return (
    <> <div className="flex-1 p-4 bg-gray-50 overflow-y-auto">
          <div className="space-y-4">
            {/* Incoming message */}
            <div className="flex items-start space-x-2">
              <div className="w-8 h-8 bg-teal-600 rounded-full flex items-center justify-center text-white text-sm">
                JD
              </div>
              <div className="bg-white rounded-lg p-3 shadow-sm max-w-xs">
                <p className="text-sm text-gray-900">Hi! I'm interested in your product catalog. Can you share it with me?</p>
                <p className="text-xs text-gray-500 mt-1">10:30 AM</p>
              </div>
            </div>

            {/* Outgoing message */}
            <div className="flex items-start space-x-2 flex-row-reverse">
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 text-sm">
                You
              </div>
              <div className="bg-teal-600 text-white rounded-lg p-3 shadow-sm max-w-xs">
                <p className="text-sm">Sure! I'll send you our latest catalog right away. It includes all our new products for this season.</p>
                <p className="text-xs text-teal-100 mt-1">10:32 AM</p>
              </div>
            </div>

            {/* Incoming message */}
            <div className="flex items-start space-x-2">
              <div className="w-8 h-8 bg-teal-600 rounded-full flex items-center justify-center text-white text-sm">
                JD
              </div>
              <div className="bg-white rounded-lg p-3 shadow-sm max-w-xs">
                <p className="text-sm text-gray-900">Thanks for the quick response!</p>
                <p className="text-xs text-gray-500 mt-1">10:35 AM</p>
              </div>
            </div>
          </div>
        </div></>
  )
}

export default Message