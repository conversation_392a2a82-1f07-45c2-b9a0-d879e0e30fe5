import React from "react";
import { MessageSquare, Play, Settings } from "lucide-react";
import { But<PERSON> } from "@/components/shared/button";
import { Card } from "@/components/shared/card";

interface Flow {
  id: number;
  name: string;
  triggers: number;
  responses: number;
  status: "active" | "draft";
}

interface FlowListProps {
  flows: Flow[];
  onEditFlow?: (flowId: number) => void;
  onRunFlow?: (flowId: number) => void;
}

const FlowList: React.FC<FlowListProps> = ({ flows, onEditFlow, onRunFlow }) => {
  return (
    <div className="lg:col-span-2">
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Your Flows
        </h3>
        <div className="space-y-4">
          {flows.map((flow) => (
            <div
              key={flow.id}
              className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center">
                    <MessageSquare className="w-4 h-4 text-teal-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">
                      {flow.name}
                    </h4>
                    <p className="text-sm text-gray-500">
                      {flow.triggers} triggers • {flow.responses} responses
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      flow.status === "active"
                        ? "bg-green-100 text-green-800"
                        : "bg-yellow-100 text-yellow-800"
                    }`}
                  >
                    {flow.status}
                  </span>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => onRunFlow?.(flow.id)}
                  >
                    <Play className="w-4 h-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => onEditFlow?.(flow.id)}
                  >
                    <Settings className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default FlowList;
