
import React from 'react';
import { Search, Filter, MoreVertical, Phone, Video } from 'lucide-react';
import { Input } from '@/components/shared/input';
import { Button } from '@/components/shared/button';
import ConversationList from '@/components/pagewise/inbox/ConversationList';
import
{conversations} from '@/components/pagewise/inbox/Conversation';
import Message from '@/components/pagewise/inbox/Message';
const Inbox = () => {
  

  return (
    <div className="h-[calc(100vh-8rem)] flex bg-white rounded-xl border border-gray-200 overflow-hidden">
      {/* Conversations List */}
      <div className="w-1/3 border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Conversations</h2>
            <Button variant="ghost" size="sm">
              <Filter className="w-4 h-4" />
            </Button>
          </div>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              type="text"
              placeholder="Search conversations..."
              className="pl-10 bg-gray-50 border-0"
            />
          </div>
        </div>
{/* Conversation List */}
        <ConversationList/>
        
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="p-4 border-b border-gray-200 bg-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-teal-600 rounded-full flex items-center justify-center text-white font-medium">
                JD
              </div>
              <div>
                <h3 className="font-medium text-gray-900">John Doe</h3>
                <p className="text-sm text-gray-500">Online • Last seen 2 min ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm">
                <Phone className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <Video className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <MoreVertical className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Messages */}
        <Message/>

        {/* Message Input */}
        <div className="p-4 bg-white border-t border-gray-200">
          <div className="flex items-center space-x-2">
            <Input
              type="text"
              placeholder="Type your message..."
              className="flex-1"
            />
            <Button className="ayuchat-button">
              Send
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Inbox;
