
import { Toaster } from "@/components/shared/toaster";
import { Toaster as Son<PERSON> } from "@/components/shared/sonner";
import { TooltipProvider } from "@/components/shared/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import Dashboard from "./components/pagewise/dashboard/Dashboard";
import Inbox from "./pages/Inbox";
import ChatbotBuilder from "./components/pagewise/chatbot/ChatbotBuilder";
import Templates from "./components/pagewise/templates/Templates";
import Campaigns from "./components/pagewise/campaigns/Campaigns";
import Contacts from "./components/pagewise/contacts/Contacts";
import Analytics from "./components/pagewise/analytics/Analytics";
import Integration from "./components/pagewise/integration/Integration";
import Settings from "./components/pagewise/settings/Settings";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/inbox" element={<Inbox />} />
            <Route path="/chatbot" element={<ChatbotBuilder />} />
            <Route path="/templates" element={<Templates />} />
            <Route path="/campaigns" element={<Campaigns />} />
            <Route path="/contacts" element={<Contacts />} />
            <Route path="/analytics" element={<Analytics />} />
            <Route path="/integration" element={<Integration />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </Layout>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
