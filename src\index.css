
@tailwind base;
@tailwind components;
@tailwind utilities;

/* AyuChat Design System - WhatsApp SaaS Platform */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 180 85% 25%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 180 100% 38%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 180 85% 25%;

    --radius: 0.75rem;

    --sidebar-background: 248 250% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 180 85% 25%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 180 20% 95%;
    --sidebar-accent-foreground: 180 85% 25%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 180 100% 38%;

    /* Custom AyuChat Variables */
    --ayuchat-teal: 180 85% 25%;
    --ayuchat-aqua: 180 100% 38%;
    --ayuchat-light-bg: 248 250% 98%;
    --ayuchat-gradient: linear-gradient(135deg, hsl(180 85% 25%) 0%, hsl(180 100% 38%) 100%);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 180 100% 38%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 180 85% 25%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 180 100% 38%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 180 100% 38%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 180 100% 38%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  }

  .ayuchat-gradient {
    background: linear-gradient(135deg, hsl(var(--ayuchat-teal)) 0%, hsl(var(--ayuchat-aqua)) 100%);
  }

  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

@layer components {
  .ayuchat-card {
    @apply bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-200;
  }

  .ayuchat-button {
    @apply bg-gradient-to-r from-[hsl(var(--ayuchat-teal))] to-[hsl(var(--ayuchat-aqua))] text-white font-medium py-2 px-4 rounded-lg hover:opacity-90 transition-opacity duration-200;
  }

  .ayuchat-stat {
    @apply bg-white rounded-lg p-4 border border-gray-100 hover:border-gray-200 transition-colors duration-200;
  }
}
